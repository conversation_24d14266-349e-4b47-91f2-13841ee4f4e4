black.cpp和white.cpp分别为执黑子和执白子时的策略代码，将会从chessboard.txt中读取棋盘
其中的get_local()函数返回在棋盘上落子的坐标，并输出到blackfile.txt或whitefile.txt中，你的任务是完善这个函数，并按照要求输出棋子的坐标
编写完black.cpp和white.cpp之后，编译运行会得到可执行文件black.exe以及white.exe
Othello.py为平台本身，此时可以双击运行实现黑棋和白棋的对弈
Othello.py中的代码请勿改动，以免影响平台的正常运行
当分出胜负时，会同步生成log.txt以及screenshot.jpg
前者为这场对决的历史记录，后者为分出胜负时棋盘状态的截图

sample文件夹中有用于测试的黑棋/白棋程序，你可以复制到与Othello.py相同的根目录下进行测试。

关于你的代码的输入和输出格式：
chessboard.txt中存储的是长度为64的字符串，该字符串仅包含数字字符'0','1'或者'2'，其中第i个字符表示第((i-1)/8+1)行第((i-1)%8+1)列的棋子，'0'表示该位置为空位,'1'表示该位置棋子为白棋，'2'表示该位置棋子为黑棋。
你输出到blackfile.txt或者whitefile.txt文件的内容必须是一个长度刚好为2的字符串，其中第一个字符必须为'A'~'H'中的某个，表示你的行；第二个字符必须为'1'~'8'中的某个，表示你的列；比如假如你要下棋到第3行第4列这个位置，你就需要输出"C4"；另外，如果你判断你已经没有位置可以下棋，请必须输出"00"，否则你的程序将会被判错。
任何输出不为长度为2的字符串，或下到非法位置的行为皆会直接被判负。

