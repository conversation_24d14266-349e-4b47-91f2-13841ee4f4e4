#include<bits/stdc++.h>
using namespace std;
string st;
int mp[10][10];

// 检查在(x,y)位置落黑子是否合法
bool isValidMove(int x, int y, int player) {
    // 如果位置已被占用，则不合法
    if (mp[x][y] != 0) return false;

    // 八个方向：上、右上、右、右下、下、左下、左、左上
    int dx[] = {-1, -1, 0, 1, 1, 1, 0, -1};
    int dy[] = {0, 1, 1, 1, 0, -1, -1, -1};

    // 检查每个方向是否能翻转对方棋子
    for (int dir = 0; dir < 8; dir++) {
        int nx = x + dx[dir];
        int ny = y + dy[dir];
        bool hasOpponent = false;

        // 沿着当前方向搜索
        while (nx >= 1 && nx <= 8 && ny >= 1 && ny <= 8) {
            if (mp[nx][ny] == 0) break; // 遇到空位，停止搜索

            if (mp[nx][ny] == (3 - player)) { // 遇到对方棋子
                hasOpponent = true;
            } else if (mp[nx][ny] == player && hasOpponent) { // 遇到己方棋子且中间有对方棋子
                return true; // 找到合法落子位置
            } else {
                break; // 遇到己方棋子但中间没有对方棋子
            }

            nx += dx[dir];
            ny += dy[dir];
        }
    }

    return false;
}

// 计算在(x,y)位置落子能翻转的棋子数量
int countFlips(int x, int y, int player) {
    if (!isValidMove(x, y, player)) return 0;

    int totalFlips = 0;
    int dx[] = {-1, -1, 0, 1, 1, 1, 0, -1};
    int dy[] = {0, 1, 1, 1, 0, -1, -1, -1};

    for (int dir = 0; dir < 8; dir++) {
        int nx = x + dx[dir];
        int ny = y + dy[dir];
        int flips = 0;

        // 计算这个方向能翻转的棋子数
        while (nx >= 1 && nx <= 8 && ny >= 1 && ny <= 8) {
            if (mp[nx][ny] == 0) break;

            if (mp[nx][ny] == (3 - player)) {
                flips++;
            } else if (mp[nx][ny] == player) {
                totalFlips += flips;
                break;
            } else {
                break;
            }

            nx += dx[dir];
            ny += dy[dir];
        }
    }

    return totalFlips;
}

string get_local()
{
    // 黑棋用数字2表示
    int player = 2;

    // 存储所有合法落子位置及其评分
    vector<pair<int, pair<int, int>>> validMoves;

    // 遍历整个棋盘寻找合法落子位置
    for (int i = 1; i <= 8; i++) {
        for (int j = 1; j <= 8; j++) {
            if (isValidMove(i, j, player)) {
                // 计算这个位置的评分（翻转的棋子数量）
                int score = countFlips(i, j, player);

                // 角落位置加分（角落是最有价值的位置）
                if ((i == 1 || i == 8) && (j == 1 || j == 8)) {
                    score += 100;
                }
                // 边缘位置加分
                else if (i == 1 || i == 8 || j == 1 || j == 8) {
                    score += 10;
                }

                validMoves.push_back({score, {i, j}});
            }
        }
    }

    // 如果没有合法落子位置，返回"00"
    if (validMoves.empty()) {
        return "00";
    }

    // 按评分降序排序，选择最佳位置
    sort(validMoves.begin(), validMoves.end(), greater<pair<int, pair<int, int>>>());

    // 获取最佳落子位置
    int bestRow = validMoves[0].second.first;
    int bestCol = validMoves[0].second.second;

    // 将坐标转换为字符串格式（A-H表示行，1-8表示列）
    string ans = "";
    ans += char('A' + bestRow - 1); // 行：1->A, 2->B, ..., 8->H
    ans += char('0' + bestCol);     // 列：1->1, 2->2, ..., 8->8

    return ans;
}

int main()
{
	freopen("chessboard.txt","r",stdin);
	freopen("blackfile.txt","w",stdout);
	srand(time(0));
	cin>>st;
	int now=0;
	for (int i=1;i<=8;i++)
		for (int j=1;j<=8;j++)
			{
				mp[i][j]=st[now]-48;
				now++;
			}
	cout<<get_local();
	return 0;
}
