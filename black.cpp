#include<bits/stdc++.h>
using namespace std;
string st;
int mp[10][10];

// 检查在(x,y)位置落黑子是否合法
bool validmove(int x, int y, int player) {
    // 如果位置已被占用，则不合法
    if (mp[x][y] != 0) return false;

    // 八个方向：上、右上、右、右下、下、左下、左、左上
    int dx[] = {-1, -1, 0, 1, 1, 1, 0, -1};
    int dy[] = {0, 1, 1, 1, 0, -1, -1, -1};

    // 检查每个方向是否能翻转对方棋子
    for (int dir = 0; dir < 8; dir++) {
        int nx = x + dx[dir];
        int ny = y + dy[dir];
        bool flag = false;

        // 沿着当前方向搜索
        while (nx >= 1 && nx <= 8 && ny >= 1 && ny <= 8) {
            if (mp[nx][ny] == 0) break; // 遇到空位，停止搜索

            if (mp[nx][ny] == (3 - player)) { // 遇到对方棋子
                flag = true;
            } else if (mp[nx][ny] == player && flag) { // 遇到己方棋子且中间有对方棋子
                return true; // 找到合法落子位置
            } else {
                break; // 遇到己方棋子但中间没有对方棋子
            }

            nx += dx[dir];
            ny += dy[dir];
        }
    }

    return false;
}

// 计算在(x,y)位置落子能翻转的棋子数量
int countflip(int x, int y, int player) {
    if (!validmove(x, y, player)) return 0;

    int totalFlips = 0;
    int dx[] = {-1, -1, 0, 1, 1, 1, 0, -1};
    int dy[] = {0, 1, 1, 1, 0, -1, -1, -1};

    for (int dir = 0; dir < 8; dir++) {
        int nx = x + dx[dir];
        int ny = y + dy[dir];
        int flips = 0;

        // 计算这个方向能翻转的棋子数
        while (nx >= 1 && nx <= 8 && ny >= 1 && ny <= 8) {
            if (mp[nx][ny] == 0) break;

            if (mp[nx][ny] == (3 - player)) {
                flips++;
            } else if (mp[nx][ny] == player) {
                totalFlips += flips;
                break;
            } else {
                break;
            }

            nx += dx[dir];
            ny += dy[dir];
        }
    }

    return totalFlips;
}

// 计算当前棋盘上的棋子总数，用于判断游戏阶段
int getTotal() {
    int count = 0;
    for (int i = 1; i <= 8; i++) {
        for (int j = 1; j <= 8; j++) {
            if (mp[i][j] != 0) count++;
        }
    }
    return count;
}

// 检查某个位置是否为危险位置（紧邻角落的位置）
bool isdanger(int x, int y) {
    // X位置：紧邻角落的位置，可能让对手占角
    if ((x == 2 && y == 2) || (x == 2 && y == 7) ||
        (x == 7 && y == 2) || (x == 7 && y == 7)) return true;

    // C位置：角落旁边的边缘位置
    if ((x == 1 && (y == 2 || y == 7)) ||
        (x == 8 && (y == 2 || y == 7)) ||
        (y == 1 && (x == 2 || x == 7)) ||
        (y == 8 && (x == 2 || x == 7))) return true;

    return false;
}

// 检查角落是否已被占据
bool isOcc(int corner) {
    // corner: 0=左上, 1=右上, 2=左下, 3=右下
    int corners[4][2] = {{1,1}, {1,8}, {8,1}, {8,8}};
    return mp[corners[corner][0]][corners[corner][1]] != 0;
}

// 计算稳定性评分（不易被翻转的棋子）
int calStable(int x, int y, int player) {
    int stability = 0;

    // 角落位置稳定性最高
    if ((x == 1 || x == 8) && (y == 1 || y == 8)) {
        stability += 1000;
    }
    // 边缘位置稳定性较高
    else if (x == 1 || x == 8 || y == 1 || y == 8) {
        stability += 50;

        // 如果相邻的角落被己方占据，稳定性更高
        if (x == 1 && ((y > 1 && mp[1][1] == player) || (y < 8 && mp[1][8] == player))) stability += 30;
        if (x == 8 && ((y > 1 && mp[8][1] == player) || (y < 8 && mp[8][8] == player))) stability += 30;
        if (y == 1 && ((x > 1 && mp[1][1] == player) || (x < 8 && mp[8][1] == player))) stability += 30;
        if (y == 8 && ((x > 1 && mp[1][8] == player) || (x < 8 && mp[8][8] == player))) stability += 30;
    }

    return stability;
}

// 计算机动性（对手可选择的位置数量变化）
int calMobility(int x, int y, int player) {
    // 模拟落子后对手的可选位置数量
    int originalMp[10][10];
    // 备份原棋盘
    for (int i = 1; i <= 8; i++) {
        for (int j = 1; j <= 8; j++) {
            originalMp[i][j] = mp[i][j];
        }
    }

    // 模拟落子
    mp[x][y] = player;

    // 计算对手的可选位置数量
    int canmoves = 0;
    int opponent = 3 - player;
    for (int i = 1; i <= 8; i++) {
        for (int j = 1; j <= 8; j++) {
            if (validmove(i, j, opponent)) {
                canmoves++;
            }
        }
    }

    // 恢复棋盘
    for (int i = 1; i <= 8; i++) {
        for (int j = 1; j <= 8; j++) {
            mp[i][j] = originalMp[i][j];
        }
    }

    // 减少对手选择越多越好（返回负值）
    return -canmoves * 5;
}

string get_local()
{
    // 黑棋用数字2表示
    int player = 2;
    int totalPieces = getTotal();

    // 存储所有合法落子位置及其评分
    vector<pair<int, pair<int, int>>> validMoves;

    // 遍历整个棋盘寻找合法落子位置
    for (int i = 1; i <= 8; i++) {
        for (int j = 1; j <= 8; j++) {
            if (validmove(i, j, player)) {
                int score = 0;

                // 基础分：翻转的棋子数量
                int flips = countflip(i, j, player);

                // 游戏阶段判断
                if (totalPieces < 20) {
                    // 开局阶段：重视位置价值，不过分追求翻转数量
                    score = flips * 2;
                } else if (totalPieces < 50) {
                    // 中局阶段：平衡位置价值和翻转数量
                    score = flips * 3;
                } else {
                    // 残局阶段：重视翻转数量，争取更多棋子
                    score = flips * 5;
                }

                // 位置价值评估
                if ((i == 1 || i == 8) && (j == 1 || j == 8)) {
                    // 角落位置：绝对优先
                    score += 2000;
                } else if (isdanger(i, j)) {
                    // 危险位置：避免给对手占角机会
                    score -= 500;

                    // 但如果相邻角落已被己方占据，则危险性降低
                    if ((i == 2 && j == 2 && mp[1][1] == player) ||
                        (i == 2 && j == 7 && mp[1][8] == player) ||
                        (i == 7 && j == 2 && mp[8][1] == player) ||
                        (i == 7 && j == 7 && mp[8][8] == player)) {
                        score += 300; // 减少惩罚
                    }
                } else {
                    // 稳定性评分
                    score += calStable(i, j, player);

                    // 机动性评分（仅在中局考虑，这样就可以避免计算开销）
                    if (totalPieces >= 20 && totalPieces < 50) {
                        score += calMobility(i, j, player);
                    }
                }

                validMoves.push_back({score, {i, j}});
            }
        }
    }

    // 如果没有合法落子位置，返回"00"
    if (validMoves.empty()) {
        return "00";
    }

    // 按评分降序排序，选择最佳位置
    sort(validMoves.begin(), validMoves.end(), greater<pair<int, pair<int, int>>>());

    // 获取最佳落子位置
    int bestRow = validMoves[0].second.first;
    int bestCol = validMoves[0].second.second;

    // 将坐标转换为字符串格式（A-H表示行，1-8表示列）
    string ans = "";
    ans += char('A' + bestRow - 1); // 行：1->A, 2->B, ..., 8->H
    ans += char('0' + bestCol);     // 列：1->1, 2->2, ..., 8->8

    return ans;
}

int main()
{
	freopen("chessboard.txt","r",stdin);
	freopen("blackfile.txt","w",stdout);
	srand(time(0));
	cin>>st;
	int now=0;
	for (int i=1;i<=8;i++)
		for (int j=1;j<=8;j++)
			{
				mp[i][j]=st[now]-48;
				now++;
			}
	cout<<get_local();
	return 0;
}
