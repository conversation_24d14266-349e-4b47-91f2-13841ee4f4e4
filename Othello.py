import pygame
import math
import msvcrt
import os

from sys import exit
from tkinter import *

dx=[-1,-1,0,1,1,1,0,-1];
dy=[0,1,1,1,0,-1,-1,-1];

game_map = [[0,0,0,0,0,0,0,0],
            [0,0,0,0,0,0,0,0],
            [0,0,0,0,0,0,0,0],
            [0,0,0,1,2,0,0,0],
            [0,0,0,2,1,0,0,0],
            [0,0,0,0,0,0,0,0],
            [0,0,0,0,0,0,0,0],
            [0,0,0,0,0,0,0,0]]

pygame.init()
screen = pygame.display.set_mode((630,630))
pygame.display.set_caption("othello")
clock = pygame.time.Clock()
background = pygame.image.load("./assets/background.png")
white = pygame.image.load("./assets/white.png")
black = pygame.image.load("./assets/black.png")
screen.blit(background,(0,0))
log=open("log.txt","w")
no_crash = True
for i in range(0,8):
    for j in range(0,8):
        if game_map[i][j] == 1:
            screen.blit(white,((j+1)*70+10,(i+1)*70+10))
        if game_map[i][j] == 2:
            screen.blit(black,((j+1)*70+10,(i+1)*70+10))

def end_game():
    if no_crash == True:
        black=0
        white=0
        for i in range(0,8):
            for j in range(0,8):
                if game_map[i][j] == 2:
                    black+=1
                if game_map[i][j] == 1:
                    white+=1
        if black > white:
            print("黑棋子数大于白棋子数，黑棋获胜！")
            log.write("黑棋子数大于白棋子数，黑棋获胜！")
        if black == white:
            print("黑棋子数等于白棋子数，双方平局！")
            log.write("黑棋子数等于白棋子数，双方平局！")
        if black < white:
            print("黑棋子数小于白棋子数，白棋获胜！")
            log.write("黑棋子数小于白棋子数，白棋获胜！")
    log.close()
    pygame.image.save(screen,"screenshot.jpg")
    pygame.time.delay(1000)
    pygame.quit()
    exit()
            
def dep_check(now,x,y,dx,dy):
    while True:
        x=x+dx
        y=y+dy
        if x<0 or x>7 or y<0 or y>7:
            return False
        if game_map[x][y] != 1 and game_map[x][y] !=2:
            return False
        if now == 2:
            if game_map[x][y] == 2:
                return True
        if now == 1:
            if game_map[x][y] == 1:
                return True
            
def check(now):
    if now == 2:
        for i in range(0,8):
            for j in range(0,8):
                if game_map[i][j] != 1 and game_map[i][j] != 2:
                    for k in range(0,8):
                        if i+dx[k]<0 or i+dx[k]>7 or j+dy[k]<0 or j+dy[k]>7:
                            continue
                        if game_map[i+dx[k]][j+dy[k]] == 1:
                            if dep_check(now,i,j,dx[k],dy[k]) == True:
                                return True
        return False
    if now == 1:
        for i in range(0,8):
            for j in range(0,8):
                if game_map[i][j] !=1 and game_map[i][j] !=2:
                    for k in range(0,8):
                        if i+dx[k]<0 or i+dx[k]>7 or j+dy[k]<0 or j+dy[k]>7:
                            continue
                        if game_map[i+dx[k]][j+dy[k]] == 2:
                            if dep_check(now,i,j,dx[k],dy[k]) == True:
                                return True
        return False
                        
def check_low(now,x,y):
    if game_map[x][y] == 1 or game_map[x][y] == 2:
        return False;
    if now == 2:
        for i in range(0,8):
            newx=x+dx[i]
            newy=y+dy[i]
            if newx<0 or newx>7 or newy<0 or newy>7:
                continue
            if game_map[newx][newy] != 1:
                continue
            while True:
                newx+=dx[i]
                newy+=dy[i]
                if newx<0 or newx>7 or newy<0 or newy>7:
                    break
                if game_map[newx][newy] != 2 and game_map[newx][newy] != 1:
                    break
                if game_map[newx][newy] == 2:
                    return True
        return False
    if now == 1:
        for i in range(0,8):
            newx=x+dx[i]
            newy=y+dy[i]
            if newx<0 or newx>7 or newy<0 or newy>7:
                continue
            if game_map[newx][newy] != 2:
                continue
            while True:
                newx+=dx[i]
                newy+=dy[i]
                if newx<0 or newx>7 or newy<0 or newy>7:
                    break
                if game_map[newx][newy] !=2 and game_map[newx][newy] != 1:
                    break
                if game_map[newx][newy] == 1:
                    return True
        return False
    
def trans(now,numx,numy):
    if now == 2:
        for i in range(0,8):
            newx=numx
            newy=numy
            f=0
            while True:
                newx+=dx[i]
                newy+=dy[i]
                if newx<0 or newx>7 or newy<0 or newy>7:
                    break
                if game_map[newx][newy] != 2 and game_map[newx][newy] !=1:
                    break
                if game_map[newx][newy] == 2:
                    f=1
                    break
            if f == 1:
                x=numx
                y=numy
                while x != newx or y != newy:
                    game_map[x][y]=2
                    screen.blit(black,((y+1)*70+10,(x+1)*70+10))
                    pygame.display.update()
                    pygame.time.delay(500)
                    x+=dx[i]
                    y+=dy[i]
    if now == 1:
        for i in range(0,8):
            newx=numx
            newy=numy
            f=0
            while True:
                newx+=dx[i]
                newy+=dy[i]
                if newx<0 or newx>7 or newy<0 or newy>7:
                    break
                if game_map[newx][newy] != 2 and game_map[newx][newy] !=1:
                    break
                if game_map[newx][newy] == 1:
                    f=1
                    break
            if f == 1:
                x=numx
                y=numy
                while x != newx or y != newy:
                    game_map[x][y]=1
                    screen.blit(white,((y+1)*70+10,(x+1)*70+10))
                    pygame.display.update()
                    pygame.time.delay(500)
                    x+=dx[i]
                    y+=dy[i]

def calc_chess():
    black=0
    white=0
    for i in range(0,8):
        for j in range(0,8):
            if game_map[i][j] == 2:
                black+=1
            if game_map[i][j] == 1:
                white+=1
    print("黑棋：",black,"白棋：",white)
    log.write("黑棋："+str(black)+"\t白棋："+str(white)+'\n')
        
def black_take():
    os.system("black.exe")
    blackfile=open("blackfile.txt",'r')
    blacktake=blackfile.read()
    blackfile.close()
    print("黑方这步棋所下的位置为：",blacktake)
    log.write("黑方这步棋所下的位置为："+blacktake+'\n')
    if len(blacktake) != 2:
        print("非法输入！白方获胜。")
        log.write("非法输入！白方获胜。")
        no_crash = False
        end_game()
    x=ord(blacktake[0])-65
    y=ord(blacktake[1])-49
    if x<0 or x>7 or y<0 or y>7:
        print("这步棋的位置超出棋盘！白方获胜。")
        log.write("这步棋的位置超出棋盘！白方获胜。")
        no_crash = False
        end_game()
    if check_low(2,x,y) == False:
        print("这步棋的位置非法！白方获胜。")
        log.write("这步棋的位置非法！白方获胜。")
        no_crash = False
        end_game()
    trans(2,x,y)
    calc_chess()
    chessboard=open("chessboard.txt",'w')
    for i in range(0,8):
        for j in range(0,8):
            chessboard.write(str(game_map[i][j]))
    chessboard.close()
    
def white_take():
    os.system("white.exe")
    whitefile=open("whitefile.txt",'r')
    whitetake=whitefile.read()
    whitefile.close()
    print("白方这步棋所下的位置为：",whitetake)
    log.write("白方这步棋所下的位置为："+whitetake+'\n')
    if len(whitetake) != 2:
        print("非法输入！黑方获胜。")
        log.write("非法输入！黑方获胜。")
        no_crash = False
        end_game()
    x=ord(whitetake[0])-65
    y=ord(whitetake[1])-49
    if x<0 or x>7 or y<0 or y>7:
        print("这步棋的位置超出棋盘！黑方获胜。")
        log.write("这步棋的位置超出棋盘！黑方获胜。")
        no_crash = False
        end_game()
    if check_low(1,x,y) == False:
        print("这步棋的位置非法！黑方获胜。")
        log.write("这步棋的位置非法！黑方获胜。")
        no_crash = False
        end_game()
    trans(1,x,y)
    calc_chess()
    chessboard=open("chessboard.txt",'w')
    for i in range(0,8):
        for j in range(0,8):
            chessboard.write(str(game_map[i][j]))
    chessboard.close()
    
now = 2
black_flag=True
white_flag=True
pygame.display.update()
chessboard=open("chessboard.txt",'w')
for i in range(0,8):
    for j in range(0,8):
        chessboard.write(str(game_map[i][j]))
chessboard.close()
while True:
    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            pygame.quit()
            exit()
            
    clock.tick(60)
    if check(now):
        if now == 2:
            black_flag=True
            black_take()
            now=1
        elif now == 1:
            while_flag=True
            white_take()
            now=2
    else:
        if now == 2:
            print("黑方没有可以下棋的位置！")
            log.write("黑方没有可以下棋的位置！")
            os.system("black.exe")
            blackfile=open("blackfile.txt",'r')
            blacktake=blackfile.read()
            blackfile.close()
            print("黑方这步棋所下的位置为：",blacktake)
            log.write("黑方这步棋所下的位置为："+blacktake+'\n')
            if blacktake != "00":
                print("非法输入！白方获胜。")
                log.write("非法输入！白方获胜。")
                no_crash = False
                end_game()
            black_flag=False
            now=1
        elif now == 1:
            print("白方没有可以下棋的位置！")
            log.write("白方没有可以下棋的位置！")
            os.system("white.exe")
            whitefile=open("whitefile.txt",'r')
            whitetake=whitefile.read()
            whitefile.close()
            print("白方这步棋所下的位置为：",whitetake)
            log.write("白方这步棋所下的位置为："+whitetake+'\n')
            if whitetake != "00":
                print("非法输入！黑方获胜。")
                log.write("非法输入！黑方获胜。")
                no_crash = False
                end_game()
            white_flag=False
            now=2
    if black_flag == False and white_flag == False:
        print("双方皆没有可以下棋的位置！游戏结束。")
        end_game()
        exit()
    
